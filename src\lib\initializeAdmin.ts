import { supabase } from './supabase';

export interface AdminCredentials {
  email: string;
  password: string;
}

// بيانات المدير الافتراضي
export const DEFAULT_ADMIN: AdminCredentials = {
  email: '<EMAIL>',
  password: 'Strategic@123'
};

/**
 * إنشاء مستخدم إداري افتراضي
 */
export async function initializeDefaultAdmin(): Promise<boolean> {
  try {
    console.log('🔄 جاري التحقق من وجود المدير الافتراضي...');

    // التحقق من وجود المدير الافتراضي
    const { data: existingUser } = await supabase.auth.admin.listUsers();
    const adminExists = existingUser.users.some(user => user.email === DEFAULT_ADMIN.email);

    if (adminExists) {
      console.log('✅ المدير الافتراضي موجود بالفعل');
      return true;
    }

    console.log('🔄 جاري إنشاء المدير الافتراضي...');

    // إنشاء المستخدم في نظام المصادقة
    const { data: authUser, error: authError } = await supabase.auth.admin.createUser({
      email: DEFAULT_ADMIN.email,
      password: DEFAULT_ADMIN.password,
      email_confirm: true
    });

    if (authError) {
      console.error('❌ خطأ في إنشاء المستخدم:', authError);
      return false;
    }

    // استخدام المعرفات الموجودة في قاعدة البيانات
    const adminRole = { id: '1a6bfbe0-f9d0-4377-93fd-9411d01ce444' }; // ADMIN role
    const adminDept = { id: 'bcab861a-5a84-443e-b452-2f7ab01f86ae' }; // قسم المالية والإدارة

    // إنشاء ملف المستخدم
    const { error: profileError } = await supabase
      .from('user_profiles')
      .insert({
        id: authUser.user.id,
        full_name: 'مدير النظام',
        role_id: adminRole.id,
        department_id: adminDept.id,
        is_active: true
      });

    if (profileError) {
      console.error('❌ خطأ في إنشاء ملف المستخدم:', profileError);
      return false;
    }

    console.log('✅ تم إنشاء المدير الافتراضي بنجاح');
    console.log(`📧 البريد الإلكتروني: ${DEFAULT_ADMIN.email}`);
    console.log(`🔑 كلمة المرور: ${DEFAULT_ADMIN.password}`);
    
    return true;
  } catch (error) {
    console.error('❌ خطأ عام في إنشاء المدير الافتراضي:', error);
    return false;
  }
}

/**
 * التحقق من صحة بيانات الدخول
 */
export async function validateAdminCredentials(email: string, password: string): Promise<boolean> {
  try {
    const { error } = await supabase.auth.signInWithPassword({ email, password });
    if (!error) {
      // تسجيل الخروج فوراً بعد التحقق
      await supabase.auth.signOut();
      return true;
    }
    return false;
  } catch {
    return false;
  }
}

/**
 * إعادة تعيين كلمة مرور المدير
 */
export async function resetAdminPassword(newPassword: string): Promise<boolean> {
  try {
    // البحث عن المدير
    const { data: users } = await supabase.auth.admin.listUsers();
    const adminUser = users.users.find(user => user.email === DEFAULT_ADMIN.email);

    if (!adminUser) {
      console.error('❌ لم يتم العثور على المدير');
      return false;
    }

    // تحديث كلمة المرور
    const { error } = await supabase.auth.admin.updateUserById(adminUser.id, {
      password: newPassword
    });

    if (error) {
      console.error('❌ خطأ في تحديث كلمة المرور:', error);
      return false;
    }

    console.log('✅ تم تحديث كلمة مرور المدير بنجاح');
    return true;
  } catch (error) {
    console.error('❌ خطأ عام في تحديث كلمة المرور:', error);
    return false;
  }
}

/**
 * إنشاء حساب مصادقة لمستخدم موجود في جدول users
 */
export async function createAuthAccountForUser(email: string, password: string = 'hemam2025'): Promise<boolean> {
  try {
    console.log(`🔄 جاري إنشاء حساب مصادقة للمستخدم: ${email}`);

    // التحقق من وجود المستخدم في جدول users
    const { data: userData, error: userError } = await supabase
      .from('users')
      .select('*')
      .eq('email', email)
      .single();

    if (userError || !userData) {
      console.error('❌ المستخدم غير موجود في جدول users:', userError);
      return false;
    }

    // التحقق من وجود حساب مصادقة
    const { data: existingUsers } = await supabase.auth.admin.listUsers();
    const authUserExists = existingUsers.users.some(user => user.email === email);

    if (authUserExists) {
      console.log('✅ حساب المصادقة موجود بالفعل');
      return true;
    }

    // إنشاء حساب المصادقة
    const { data: authUser, error: authError } = await supabase.auth.admin.createUser({
      email: email,
      password: password,
      email_confirm: true,
      user_metadata: {
        full_name: userData.name
      }
    });

    if (authError) {
      console.error('❌ خطأ في إنشاء حساب المصادقة:', authError);
      return false;
    }

    // تحديث auth_id في جدول users
    const { error: updateError } = await supabase
      .from('users')
      .update({
        auth_id: authUser.user.id,
        has_system_account: true,
        is_system_user: true
      })
      .eq('id', userData.id);

    if (updateError) {
      console.error('❌ خطأ في تحديث auth_id:', updateError);
      return false;
    }

    console.log('✅ تم إنشاء حساب المصادقة بنجاح');
    console.log(`📧 البريد الإلكتروني: ${email}`);
    console.log(`🔑 كلمة المرور: ${password}`);

    return true;
  } catch (error) {
    console.error('❌ خطأ عام في إنشاء حساب المصادقة:', error);
    return false;
  }
}

/**
 * إعادة تعيين كلمات المرور لجميع المستخدمين الموجودين
 */
export async function recreateAllAuthAccounts(): Promise<boolean> {
  try {
    console.log('🔄 جاري إعادة تعيين كلمات المرور لجميع المستخدمين...');

    // قائمة المستخدمين
    const userEmails = [
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>'
    ];

    let successCount = 0;
    let failCount = 0;

    for (const email of userEmails) {
      try {
        // إعادة تعيين كلمة المرور باستخدام استعلام مباشر
        const response = await fetch(`${import.meta.env.VITE_SUPABASE_URL}/rest/v1/rpc/reset_user_password`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'apikey': import.meta.env.VITE_SUPABASE_ANON_KEY,
            'Authorization': `Bearer ${import.meta.env.VITE_SUPABASE_ANON_KEY}`
          },
          body: JSON.stringify({
            user_email: email,
            new_password: 'hemam2025'
          })
        });

        if (response.ok) {
          console.log(`✅ تم إعادة تعيين كلمة المرور لـ ${email}`);
          successCount++;
        } else {
          console.error(`❌ فشل في إعادة تعيين كلمة المرور لـ ${email}:`, response.status);
          failCount++;
        }

      } catch (error) {
        console.error(`❌ خطأ في إعادة تعيين كلمة المرور لـ ${email}:`, error);
        failCount++;
      }
    }

    // إذا فشلت الطريقة الأولى، جرب طريقة مباشرة
    if (successCount === 0) {
      console.log('🔄 جاري المحاولة بطريقة مختلفة...');

      try {
        // إعادة تعيين كلمات المرور مباشرة في قاعدة البيانات
        const { error } = await supabase
          .from('auth.users')
          .update({
            encrypted_password: supabase.rpc('crypt', { password: 'hemam2025', salt: supabase.rpc('gen_salt', { type: 'bf' }) }),
            updated_at: new Date().toISOString()
          })
          .in('email', userEmails);

        if (!error) {
          console.log('✅ تم إعادة تعيين كلمات المرور بنجاح');
          return true;
        } else {
          console.error('❌ خطأ في إعادة تعيين كلمات المرور:', error);
        }
      } catch (error) {
        console.error('❌ خطأ في الطريقة البديلة:', error);
      }
    }

    console.log(`✅ تم إعادة تعيين كلمة المرور لـ ${successCount} مستخدم`);
    console.log(`❌ فشل في إعادة تعيين كلمة المرور لـ ${failCount} مستخدم`);

    return successCount > 0;
  } catch (error) {
    console.error('❌ خطأ عام في إعادة تعيين كلمات المرور:', error);
    return false;
  }
}

/**
 * حذف جميع البيانات وإعادة التهيئة
 */
export async function resetSystem(): Promise<boolean> {
  try {
    console.log('🔄 جاري إعادة تعيين النظام...');

    // حذف جميع ملفات المستخدمين
    await supabase.from('user_profiles').delete().neq('id', '00000000-0000-0000-0000-000000000000');

    // حذف جميع المستخدمين من المصادقة
    const { data: users } = await supabase.auth.admin.listUsers();
    for (const user of users.users) {
      await supabase.auth.admin.deleteUser(user.id);
    }

    // إعادة إنشاء المدير الافتراضي
    const success = await initializeDefaultAdmin();

    if (success) {
      console.log('✅ تم إعادة تعيين النظام بنجاح');
    }

    return success;
  } catch (error) {
    console.error('❌ خطأ في إعادة تعيين النظام:', error);
    return false;
  }
}
