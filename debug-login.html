<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تشخيص مشاكل تسجيل الدخول</title>
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 20px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .btn {
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
        }
        .btn-primary { background-color: #007bff; color: white; }
        .btn-success { background-color: #28a745; color: white; }
        .btn-warning { background-color: #ffc107; color: black; }
        .btn-danger { background-color: #dc3545; color: white; }
        .result {
            margin: 15px 0;
            padding: 15px;
            border-radius: 5px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
        }
        .success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .warning { background-color: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .info { background-color: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 5px;
            box-sizing: border-box;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 أداة تشخيص مشاكل تسجيل الدخول</h1>
        
        <div class="info result">
            هذه الأداة ستساعد في تشخيص وحل مشاكل تسجيل الدخول في النظام
        </div>

        <div style="margin: 20px 0;">
            <button class="btn btn-primary" onclick="runFullDiagnostic()">🔍 تشخيص شامل</button>
            <button class="btn btn-warning" onclick="checkUsers()">👥 فحص المستخدمين</button>
            <button class="btn btn-success" onclick="createAdminUser()">👨‍💼 إنشاء مستخدم إداري</button>
            <button class="btn btn-danger" onclick="clearResults()">🗑️ مسح النتائج</button>
        </div>

        <div style="margin: 20px 0;">
            <h3>اختبار تسجيل الدخول:</h3>
            <div class="form-group">
                <label>البريد الإلكتروني:</label>
                <input type="email" id="testEmail" value="<EMAIL>">
            </div>
            <div class="form-group">
                <label>كلمة المرور:</label>
                <input type="password" id="testPassword" value="Strategic@123">
            </div>
            <button class="btn btn-primary" onclick="testLogin()">🧪 اختبار تسجيل الدخول</button>
        </div>

        <div id="results"></div>
    </div>

    <script>
        // إعداد Supabase
        const supabaseUrl = 'https://ncsqltgvfioneovskwxg.supabase.co';
        const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im5jc3FsdGd2ZmlvbmVvdnNrd3hnIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk2NTYwODQsImV4cCI6MjA2NTIzMjA4NH0.8F02C4CQz79JxtW1L_ZqEPOZJEaDwQRLLfVdwxZ7NZg';
        
        const supabase = window.supabase.createClient(supabaseUrl, supabaseKey);

        function addResult(message, type = 'info') {
            const resultsDiv = document.getElementById('results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `result ${type}`;
            resultDiv.textContent = message;
            resultsDiv.appendChild(resultDiv);
            resultsDiv.scrollTop = resultsDiv.scrollHeight;
        }

        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }

        // فحص المستخدمين في قاعدة البيانات
        async function checkUsers() {
            addResult('🔍 جاري فحص المستخدمين في قاعدة البيانات...', 'info');
            
            try {
                const { data, error } = await supabase
                    .from('users')
                    .select('id, name, email, auth_id, is_system_user')
                    .limit(10);

                if (error) {
                    addResult(`❌ خطأ في قراءة جدول المستخدمين: ${error.message}`, 'error');
                    return;
                }

                if (!data || data.length === 0) {
                    addResult('⚠️ لا يوجد مستخدمين في جدول users', 'warning');
                    return;
                }

                addResult(`✅ تم العثور على ${data.length} مستخدم في قاعدة البيانات:`, 'success');
                data.forEach(user => {
                    addResult(`- ${user.name} (${user.email}) - Auth ID: ${user.auth_id || 'غير مربوط'}`, 'info');
                });

            } catch (error) {
                addResult(`❌ خطأ عام: ${error.message}`, 'error');
            }
        }

        // اختبار تسجيل الدخول
        async function testLogin() {
            const email = document.getElementById('testEmail').value;
            const password = document.getElementById('testPassword').value;

            addResult(`🧪 اختبار تسجيل الدخول لـ: ${email}`, 'info');

            try {
                // محاولة تسجيل الدخول
                const { data: authData, error: authError } = await supabase.auth.signInWithPassword({
                    email,
                    password
                });

                if (authError) {
                    addResult(`❌ فشل في المصادقة: ${authError.message}`, 'error');
                    
                    if (authError.message.includes('Invalid login credentials')) {
                        addResult('💡 الحل: تحقق من البريد الإلكتروني وكلمة المرور', 'warning');
                    } else if (authError.message.includes('Email not confirmed')) {
                        addResult('💡 الحل: البريد الإلكتروني غير مؤكد', 'warning');
                    }
                    return;
                }

                addResult(`✅ نجحت المصادقة! معرف المستخدم: ${authData.user.id}`, 'success');

                // محاولة جلب بيانات المستخدم
                const { data: userData, error: userError } = await supabase
                    .from('users')
                    .select('*')
                    .eq('auth_id', authData.user.id)
                    .single();

                if (userError) {
                    if (userError.code === 'PGRST116') {
                        addResult('⚠️ المستخدم موجود في Auth لكن غير موجود في جدول users', 'warning');
                        addResult('💡 الحل: سيتم إضافة المستخدم تلقائياً...', 'info');
                        
                        // إضافة المستخدم إلى جدول users
                        const { error: insertError } = await supabase
                            .from('users')
                            .insert({
                                auth_id: authData.user.id,
                                name: 'مدير النظام',
                                email: authData.user.email,
                                role: 'مدير',
                                department: 'الإدارة العامة',
                                position: 'مدير النظام',
                                status: 'نشط',
                                permissions: ['قراءة', 'كتابة', 'حذف', 'إدارة'],
                                has_system_account: true,
                                is_system_user: true
                            });

                        if (insertError) {
                            addResult(`❌ فشل في إضافة المستخدم: ${insertError.message}`, 'error');
                        } else {
                            addResult('✅ تم إضافة المستخدم بنجاح إلى جدول users', 'success');
                        }
                    } else {
                        addResult(`❌ خطأ في جلب بيانات المستخدم: ${userError.message}`, 'error');
                    }
                } else {
                    addResult(`✅ تم جلب بيانات المستخدم بنجاح: ${userData.name}`, 'success');
                }

                // تسجيل الخروج
                await supabase.auth.signOut();

            } catch (error) {
                addResult(`❌ خطأ عام: ${error.message}`, 'error');
            }
        }

        // إنشاء مستخدم إداري جديد
        async function createAdminUser() {
            addResult('🔧 جاري إنشاء مستخدم إداري جديد...', 'info');

            try {
                // أولاً نحاول تسجيل مستخدم جديد
                const { data: signUpData, error: signUpError } = await supabase.auth.signUp({
                    email: '<EMAIL>',
                    password: 'Strategic@123',
                    options: {
                        data: {
                            full_name: 'مدير النظام'
                        }
                    }
                });

                if (signUpError) {
                    if (signUpError.message.includes('already registered')) {
                        addResult('ℹ️ المستخدم موجود بالفعل في نظام المصادقة', 'info');
                        
                        // نحاول تسجيل الدخول للتحقق
                        const { data: signInData, error: signInError } = await supabase.auth.signInWithPassword({
                            email: '<EMAIL>',
                            password: 'Strategic@123'
                        });

                        if (signInError) {
                            addResult(`❌ فشل في تسجيل الدخول: ${signInError.message}`, 'error');
                            return;
                        }

                        addResult('✅ تم تسجيل الدخول بنجاح', 'success');
                        
                        // التحقق من وجود المستخدم في جدول users
                        const { data: existingUser, error: checkError } = await supabase
                            .from('users')
                            .select('*')
                            .eq('auth_id', signInData.user.id)
                            .single();

                        if (checkError && checkError.code === 'PGRST116') {
                            addResult('🔧 إضافة المستخدم إلى جدول users...', 'info');
                            
                            const { error: insertError } = await supabase
                                .from('users')
                                .insert({
                                    auth_id: signInData.user.id,
                                    name: 'مدير النظام',
                                    email: signInData.user.email,
                                    role: 'مدير',
                                    department: 'الإدارة العامة',
                                    position: 'مدير النظام',
                                    status: 'نشط',
                                    permissions: ['قراءة', 'كتابة', 'حذف', 'إدارة'],
                                    has_system_account: true,
                                    is_system_user: true
                                });

                            if (insertError) {
                                addResult(`❌ فشل في إضافة المستخدم: ${insertError.message}`, 'error');
                            } else {
                                addResult('✅ تم إضافة المستخدم بنجاح!', 'success');
                            }
                        } else if (existingUser) {
                            addResult('✅ المستخدم موجود بالفعل في جدول users', 'success');
                        }

                        await supabase.auth.signOut();
                        
                    } else {
                        addResult(`❌ فشل في إنشاء المستخدم: ${signUpError.message}`, 'error');
                    }
                } else {
                    addResult('✅ تم إنشاء المستخدم في نظام المصادقة', 'success');
                }

            } catch (error) {
                addResult(`❌ خطأ عام: ${error.message}`, 'error');
            }
        }

        // تشخيص شامل
        async function runFullDiagnostic() {
            addResult('🚀 بدء التشخيص الشامل...', 'info');
            
            // فحص الاتصال
            addResult('1️⃣ فحص الاتصال بقاعدة البيانات...', 'info');
            try {
                const { data, error } = await supabase.from('users').select('count').limit(1);
                if (error) {
                    addResult(`❌ فشل الاتصال: ${error.message}`, 'error');
                } else {
                    addResult('✅ الاتصال بقاعدة البيانات يعمل', 'success');
                }
            } catch (error) {
                addResult(`❌ خطأ في الاتصال: ${error.message}`, 'error');
            }

            // فحص المستخدمين
            addResult('2️⃣ فحص المستخدمين...', 'info');
            await checkUsers();

            // اختبار تسجيل الدخول
            addResult('3️⃣ اختبار تسجيل الدخول...', 'info');
            await testLogin();

            addResult('🏁 انتهى التشخيص الشامل', 'success');
        }

        // تشغيل فحص أولي عند تحميل الصفحة
        window.addEventListener('load', () => {
            addResult('🔧 أداة تشخيص مشاكل تسجيل الدخول جاهزة', 'info');
            addResult('اضغط على "تشخيص شامل" لبدء الفحص', 'info');
        });
    </script>
</body>
</html>
