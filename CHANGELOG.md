# سجل التغييرات

جميع التغييرات المهمة في هذا المشروع سيتم توثيقها في هذا الملف.

## [غير منشور]

### تم الإضافة
- صفحة اختبار تسجيل الدخول (test-login.html)
- ملف LICENSE
- تحديث شامل لملف README
- تحسينات على ملف .gitignore

### تم الإصلاح
- مشكلة تسجيل الدخول "Database error querying schema"
- إنشاء مستخدم إداري افتراضي في نظام المصادقة
- ربط المستخدم الإداري بجدول users مع auth_id صحيح

## [1.0.0] - 2025-06-20

### تم الإضافة
- نظام التخطيط الاستراتيجي الكامل
- لوحة تحكم تفاعلية مع مؤشرات الأداء
- إدارة الأهداف الاستراتيجية
- تتبع مؤشرات الأداء (KPIs)
- إدارة المشاريع والمبادرات
- تتبع الأنشطة والمهام
- إدارة المستخدمين والأقسام
- نظام تقارير شامل
- استيراد ذكي بالذكاء الاصطناعي
- نظام مصادقة آمن مع Supabase
- تصميم متجاوب

### التقنيات المستخدمة
- React 18 + TypeScript + Vite
- Tailwind CSS للتصميم
- Supabase لقاعدة البيانات والمصادقة
- ApexCharts للرسوم البيانية
- Framer Motion للحركات
- Zustand لإدارة الحالة
- React Hook Form للنماذج
- React Router DOM للتوجيه

### بيانات الدخول الافتراضية
- البريد الإلكتروني: <EMAIL>
- كلمة المرور: Strategic@123

---

## تنسيق سجل التغييرات

هذا السجل يتبع [Keep a Changelog](https://keepachangelog.com/en/1.0.0/)،
ويلتزم بـ [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

### أنواع التغييرات
- `تم الإضافة` للميزات الجديدة
- `تم التغيير` للتغييرات في الوظائف الموجودة
- `تم الإهمال` للميزات التي ستتم إزالتها قريباً
- `تم الإزالة` للميزات المحذوفة
- `تم الإصلاح` لإصلاح الأخطاء
- `الأمان` في حالة الثغرات الأمنية
