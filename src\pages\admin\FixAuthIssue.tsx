import { useState } from 'react';

const FixAuthIssue = () => {
  const [step, setStep] = useState(1);

  const users = [
    { name: 'معتصم العرفج', email: '<EMAIL>' },
    { name: 'سعد الدريهم', email: '<EMAIL>' },
    { name: 'أحمد الدريهم', email: '<EMAIL>' },
    { name: 'عبدالعزيز المطرد', email: '<EMAIL>' },
    { name: 'عبدالله المحسن', email: '<EMAIL>' },
    { name: 'سعد القحيز', email: '<EMAIL>' },
    { name: 'ناصر اليحيى', email: '<EMAIL>' }
  ];

  const sqlCommands = `
-- إصلاح جميع مشاكل المستخدمين
UPDATE auth.users
SET
  encrypted_password = crypt('hemam2025', gen_salt('bf')),
  aud = 'authenticated',
  role = 'authenticated',
  instance_id = '00000000-0000-0000-0000-000000000000',
  is_super_admin = false,
  updated_at = now(),
  raw_app_meta_data = jsonb_build_object(
    'provider', 'email',
    'providers', jsonb_build_array('email')
  )
WHERE email IN (
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>'
);

-- إصلاح جدول الهويات (identities) إذا لم يكن موجود
INSERT INTO auth.identities (id, user_id, identity_data, provider, provider_id, last_sign_in_at, created_at, updated_at)
SELECT
  gen_random_uuid(),
  u.id,
  jsonb_build_object(
    'sub', u.id::text,
    'email', u.email,
    'email_verified', true
  ),
  'email',
  u.id::text,
  now(),
  now(),
  now()
FROM auth.users u
WHERE u.email IN (
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>'
)
AND NOT EXISTS (
  SELECT 1 FROM auth.identities i
  WHERE i.user_id = u.id AND i.provider = 'email'
);

-- التحقق من نجاح العملية
SELECT
  email,
  aud,
  role,
  instance_id IS NOT NULL as has_instance_id,
  is_super_admin,
  crypt('hemam2025', encrypted_password) = encrypted_password as password_correct,
  email_confirmed_at IS NOT NULL as email_confirmed,
  (SELECT COUNT(*) FROM auth.identities WHERE user_id = auth.users.id) as identities_count
FROM auth.users
WHERE email IN (
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>'
)
ORDER BY email;
`;

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      alert('تم النسخ إلى الحافظة!');
    } catch (error) {
      alert('فشل في النسخ. يرجى النسخ يدوياً.');
    }
  };

  return (
    <div className="min-h-screen bg-gray-100 p-4">
      <div className="max-w-4xl mx-auto space-y-6">
        <div className="bg-white rounded-lg shadow-sm border border-neutral-200 p-6">
          <div className="flex items-center justify-between mb-6">
            <h1 className="text-2xl font-bold text-neutral-900">
              إصلاح مشكلة تسجيل الدخول
            </h1>
            <a 
              href="/test-login" 
              className="text-blue-600 hover:text-blue-800 text-sm underline"
            >
              اختبار تسجيل الدخول
            </a>
          </div>

          <div className="space-y-6">
            {/* شرح المشكلة */}
            <div className="bg-red-50 border border-red-200 rounded-lg p-4">
              <h3 className="font-semibold text-red-900 mb-2">🚨 المشاكل المكتشفة</h3>
              <div className="text-red-800 text-sm space-y-2">
                <p><strong>المشكلة الأساسية:</strong> التطبيق لا يملك صلاحيات Admin API المطلوبة.</p>
                <p><strong>مشاكل إضافية في بيانات المستخدمين:</strong></p>
                <ul className="list-disc list-inside space-y-1 mr-4">
                  <li>aud و role = null (يجب أن يكون "authenticated")</li>
                  <li>instance_id = null (يجب أن يكون UUID صحيح)</li>
                  <li>مشاكل في جدول identities</li>
                  <li>بيانات app_meta_data غير مكتملة</li>
                </ul>
              </div>
            </div>

            {/* الخطوات */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-gray-900">📋 خطوات الإصلاح:</h3>
              
              {/* الخطوة 1 */}
              <div className={`border rounded-lg p-4 ${step >= 1 ? 'border-blue-500 bg-blue-50' : 'border-gray-200'}`}>
                <div className="flex items-center justify-between mb-2">
                  <h4 className="font-medium text-gray-900">1. الذهاب إلى لوحة تحكم Supabase</h4>
                  <button 
                    onClick={() => setStep(1)}
                    className="text-blue-600 hover:text-blue-800 text-sm"
                  >
                    عرض
                  </button>
                </div>
                {step === 1 && (
                  <div className="space-y-2 text-sm text-gray-700">
                    <p>• اذهب إلى: <a href="https://supabase.com/dashboard" target="_blank" className="text-blue-600 underline">https://supabase.com/dashboard</a></p>
                    <p>• اختر مشروع: <strong>hemam2</strong></p>
                    <p>• اذهب إلى: <strong>SQL Editor</strong> من القائمة الجانبية</p>
                  </div>
                )}
              </div>

              {/* الخطوة 2 */}
              <div className={`border rounded-lg p-4 ${step >= 2 ? 'border-blue-500 bg-blue-50' : 'border-gray-200'}`}>
                <div className="flex items-center justify-between mb-2">
                  <h4 className="font-medium text-gray-900">2. تشغيل أمر SQL لإصلاح كلمات المرور</h4>
                  <button 
                    onClick={() => setStep(2)}
                    className="text-blue-600 hover:text-blue-800 text-sm"
                  >
                    عرض
                  </button>
                </div>
                {step === 2 && (
                  <div className="space-y-3">
                    <p className="text-sm text-gray-700">انسخ والصق الكود التالي في SQL Editor:</p>
                    <div className="bg-gray-900 text-green-400 p-4 rounded-lg text-sm font-mono overflow-x-auto">
                      <pre>{sqlCommands}</pre>
                    </div>
                    <button
                      onClick={() => copyToClipboard(sqlCommands)}
                      className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded text-sm"
                    >
                      📋 نسخ الكود
                    </button>
                  </div>
                )}
              </div>

              {/* الخطوة 3 */}
              <div className={`border rounded-lg p-4 ${step >= 3 ? 'border-blue-500 bg-blue-50' : 'border-gray-200'}`}>
                <div className="flex items-center justify-between mb-2">
                  <h4 className="font-medium text-gray-900">3. التحقق من النتائج</h4>
                  <button 
                    onClick={() => setStep(3)}
                    className="text-blue-600 hover:text-blue-800 text-sm"
                  >
                    عرض
                  </button>
                </div>
                {step === 3 && (
                  <div className="space-y-2 text-sm text-gray-700">
                    <p>• يجب أن ترى نتائج تظهر <strong>password_correct: true</strong> لجميع المستخدمين</p>
                    <p>• يجب أن ترى <strong>email_confirmed: true</strong> لجميع المستخدمين</p>
                    <p>• إذا كانت النتائج صحيحة، انتقل للخطوة التالية</p>
                  </div>
                )}
              </div>

              {/* الخطوة 4 */}
              <div className={`border rounded-lg p-4 ${step >= 4 ? 'border-green-500 bg-green-50' : 'border-gray-200'}`}>
                <div className="flex items-center justify-between mb-2">
                  <h4 className="font-medium text-gray-900">4. اختبار تسجيل الدخول</h4>
                  <button 
                    onClick={() => setStep(4)}
                    className="text-blue-600 hover:text-blue-800 text-sm"
                  >
                    عرض
                  </button>
                </div>
                {step === 4 && (
                  <div className="space-y-2 text-sm text-gray-700">
                    <p>• اذهب إلى صفحة اختبار تسجيل الدخول</p>
                    <p>• جرب أي من المستخدمين التاليين بكلمة المرور: <strong>hemam2025</strong></p>
                    <div className="bg-gray-50 p-3 rounded">
                      {users.map((user, index) => (
                        <div key={index} className="text-xs">
                          • {user.name} - {user.email}
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* معلومات إضافية */}
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <h3 className="font-semibold text-blue-900 mb-2">💡 معلومات مهمة</h3>
              <ul className="text-blue-800 text-sm space-y-1">
                <li>• كلمة المرور الجديدة لجميع المستخدمين: <strong>hemam2025</strong></li>
                <li>• هذا الإصلاح يجب أن يحل مشكلة "Invalid login credentials"</li>
                <li>• بعد الإصلاح، يمكن للمستخدمين تغيير كلمات مرورهم</li>
                <li>• إذا استمرت المشكلة، تحقق من إعدادات المصادقة في Supabase</li>
              </ul>
            </div>

            {/* أزرار التنقل */}
            <div className="flex justify-center space-x-4">
              <a 
                href="https://supabase.com/dashboard" 
                target="_blank"
                className="bg-green-600 hover:bg-green-700 text-white px-6 py-2 rounded"
              >
                🚀 فتح لوحة تحكم Supabase
              </a>
              <a 
                href="/test-login"
                className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded"
              >
                🧪 اختبار تسجيل الدخول
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default FixAuthIssue;
