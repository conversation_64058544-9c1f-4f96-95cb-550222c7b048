# نظام التخطيط الاستراتيجي

نظام شامل لإدارة التخطيط الاستراتيجي والمشاريع والأنشطة مع لوحة تحكم تفاعلية مبني بـ React و TypeScript و Supabase.

## 🚀 المميزات

- 📊 **لوحة تحكم تفاعلية** مع مؤشرات الأداء الرئيسية
- 🎯 **إدارة الأهداف الاستراتيجية** مع تتبع التقدم
- 📈 **تتبع مؤشرات الأداء (KPIs)** مع رسوم بيانية
- 🚀 **إدارة المشاريع والمبادرات** مع جدولة زمنية
- 📋 **تتبع الأنشطة والمهام** مع تعيين المسؤوليات
- 👥 **إدارة المستخدمين والأقسام** مع صلاحيات متقدمة
- 📊 **تقارير شاملة ومرئية** قابلة للتصدير
- 🤖 **استيراد ذكي بالذكاء الاصطناعي** للبيانات
- 🔐 **نظام مصادقة آمن** مع Supabase
- 📱 **تصميم متجاوب** يعمل على جميع الأجهزة

## 🛠️ التقنيات المستخدمة

- **Frontend**: React 18 + TypeScript + Vite
- **Styling**: Tailwind CSS
- **Database**: Supabase (PostgreSQL)
- **Authentication**: Supabase Auth
- **Charts**: ApexCharts
- **Icons**: Lucide React
- **Animations**: Framer Motion
- **State Management**: Zustand
- **Forms**: React Hook Form
- **Routing**: React Router DOM

## إعداد المشروع

### 1. تثبيت المتطلبات

```bash
npm install
```

### 2. متغيرات البيئة

تم إعداد ملف `.env` مع الإعدادات التالية:

```env
VITE_SUPABASE_URL=https://ncsqltgvfioneovskwxg.supabase.co
VITE_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.8F02C4CQz79JxtW1L_ZqEPOZJEaDwQRLLfVdwxZ7NZg
```

### 3. تشغيل المشروع محلياً

```bash
npm run dev
```

### 4. بناء المشروع للإنتاج

```bash
npm run build
```

## 🔧 الإصلاحات الجديدة

### ما تم إصلاحه:
- ✅ إضافة أنواع البيانات المفقودة (`user_profiles`, `roles`)
- ✅ إنشاء خدمة إدارة المستخدمين (`UserService`)
- ✅ نظام إنشاء المدير الافتراضي تلقائياً
- ✅ صفحة إعداد النظام (`/setup`)
- ✅ تحديث نظام المصادقة مع ملفات المستخدمين
- ✅ توحيد بيانات الدخول

### المشاكل المتبقية:
- ⚠️ خطأ في استيراد أيقونة `Lightbulb` في `Activities.tsx`
- ⚠️ تحذيرات React Router للإصدار المستقبلي

## 🚀 إعداد النظام للمرة الأولى

### الخطوة 1: إعداد النظام
1. بعد تشغيل المشروع، انتقل إلى: `http://localhost:5173/setup`
2. اضغط على "إعداد النظام" لإنشاء المدير الافتراضي
3. انتظر حتى اكتمال الإعداد

### الخطوة 2: تسجيل الدخول
بعد إكمال الإعداد، استخدم البيانات التالية:

- **البريد الإلكتروني**: `<EMAIL>`
- **كلمة المرور**: `Strategic@123`

⚠️ **مهم**: يُنصح بتغيير كلمة المرور بعد تسجيل الدخول الأول

## النشر على Netlify

تم إعداد المشروع للنشر على Netlify:

1. **ملف `netlify.toml`**: يحتوي على إعدادات البناء والتوجيه
2. **ملف `public/_redirects`**: لضمان عمل التوجيه بشكل صحيح
3. **متغيرات البيئة**: مُعدة في ملف التكوين

## 🧪 اختبار النظام

يمكنك اختبار تسجيل الدخول باستخدام ملف `test-login.html` المرفق:

1. افتح الملف في المتصفح
2. استخدم بيانات الدخول الافتراضية
3. تحقق من عمل الاتصال بقاعدة البيانات

## 📁 هيكل المشروع

```
src/
├── components/          # المكونات القابلة لإعادة الاستخدام
│   ├── ui/             # مكونات واجهة المستخدم الأساسية
│   ├── charts/         # مكونات الرسوم البيانية
│   ├── layouts/        # تخطيطات الصفحات
│   └── smart-import/   # مكونات الاستيراد الذكي
├── pages/              # صفحات التطبيق
│   ├── dashboard/      # لوحة التحكم
│   ├── objectives/     # الأهداف الاستراتيجية
│   ├── kpis/          # مؤشرات الأداء
│   ├── projects/      # المشاريع
│   ├── activities/    # الأنشطة
│   ├── users/         # إدارة المستخدمين
│   └── reports/       # التقارير
├── contexts/           # React Contexts
├── lib/               # المكتبات والخدمات
├── hooks/             # Custom Hooks
└── types/             # تعريفات TypeScript
```

## 🤝 المساهمة

نرحب بالمساهمات! يرجى:

1. Fork المشروع
2. إنشاء branch جديد للميزة (`git checkout -b feature/amazing-feature`)
3. Commit التغييرات (`git commit -m 'Add amazing feature'`)
4. Push إلى Branch (`git push origin feature/amazing-feature`)
5. إنشاء Pull Request

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT.

## 📞 الدعم

للحصول على الدعم:
- إنشاء [issue](https://github.com/sh33hemam/-2/issues) في GitHub
- التواصل مع فريق التطوير

---

**تم تطوير هذا النظام بواسطة فريق التطوير في مؤسسة همم** 🚀

### خطوات النشر:

1. ادفع الكود إلى GitHub
2. اربط المستودع بـ Netlify
3. سيتم النشر تلقائياً

## قاعدة البيانات

تم إنشاء مشروع Supabase جديد خصيصاً لهذا النظام:

- **اسم المشروع**: strategic-planning-system
- **المعرف**: jqtguuuzezhorpyydhwz
- **المنطقة**: ap-southeast-2

## الهيكل

```
src/
├── components/          # المكونات القابلة لإعادة الاستخدام
├── contexts/           # سياقات React (Auth)
├── lib/               # إعدادات المكتبات (Supabase)
├── pages/             # صفحات التطبيق
└── main.tsx           # نقطة الدخول الرئيسية
```

## المساهمة

1. Fork المشروع
2. أنشئ فرع للميزة الجديدة
3. اعمل Commit للتغييرات
4. ادفع إلى الفرع
5. افتح Pull Request

## الترخيص

هذا المشروع مرخص تحت رخصة MIT.
