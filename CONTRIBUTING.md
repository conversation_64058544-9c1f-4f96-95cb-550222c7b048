# دليل المساهمة

نرحب بمساهماتكم في تطوير نظام التخطيط الاستراتيجي! هذا الدليل سيساعدكم على فهم كيفية المساهمة بفعالية.

## 🚀 كيفية المساهمة

### 1. إعداد البيئة المحلية

```bash
# استنساخ المشروع
git clone https://github.com/sh33hemam/-2.git
cd -2

# تثبيت التبعيات
npm install

# تشغيل المشروع محلياً
npm run dev
```

### 2. إنشاء Branch جديد

```bash
# إنشاء branch جديد للميزة
git checkout -b feature/اسم-الميزة

# أو لإصلاح خطأ
git checkout -b fix/وصف-الإصلاح
```

### 3. تطوير الميزة

- اتبع معايير الكود الموجودة
- أضف تعليقات واضحة باللغة العربية
- تأكد من أن الكود يعمل بشكل صحيح
- اختبر التغييرات على أجهزة مختلفة

### 4. فحص الكود

```bash
# فحص TypeScript
npx tsc --noEmit

# فحص ESLint
npm run lint

# بناء المشروع
npm run build
```

### 5. إرسال Pull Request

1. ادفع التغييرات إلى branch الخاص بك
2. أنشئ Pull Request على GitHub
3. اكتب وصفاً واضحاً للتغييرات
4. انتظر المراجعة والموافقة

## 📋 معايير الكود

### TypeScript
- استخدم TypeScript بدلاً من JavaScript
- عرّف الأنواع بوضوح
- تجنب استخدام `any`

### React
- استخدم Functional Components مع Hooks
- اتبع نمط تسمية PascalCase للمكونات
- استخدم `memo` للمكونات التي تحتاج تحسين الأداء

### CSS/Tailwind
- استخدم Tailwind CSS للتصميم
- اتبع نظام الألوان الموحد
- تأكد من التصميم المتجاوب

### التسمية
- استخدم أسماء واضحة ومعبرة
- التعليقات باللغة العربية
- أسماء المتغيرات والدوال بالإنجليزية

## 🐛 الإبلاغ عن الأخطاء

عند العثور على خطأ:

1. تحقق من Issues الموجودة
2. أنشئ Issue جديد إذا لم يكن موجوداً
3. اكتب وصفاً مفصلاً للمشكلة
4. أرفق لقطات شاشة إن أمكن
5. اذكر خطوات إعادة إنتاج المشكلة

## ✨ اقتراح ميزات جديدة

لاقتراح ميزة جديدة:

1. أنشئ Issue بعنوان "Feature Request: ..."
2. اشرح الميزة المطلوبة بالتفصيل
3. اذكر الفائدة من هذه الميزة
4. أرفق mockups أو تصاميم إن أمكن

## 📝 معايير Commit Messages

استخدم الصيغة التالية:

```
نوع: وصف مختصر

وصف مفصل للتغييرات (اختياري)
```

أنواع الـ commits:
- `feat`: ميزة جديدة
- `fix`: إصلاح خطأ
- `docs`: تحديث التوثيق
- `style`: تغييرات التصميم
- `refactor`: إعادة هيكلة الكود
- `test`: إضافة أو تحديث الاختبارات
- `chore`: مهام صيانة

مثال:
```
feat: إضافة صفحة إدارة المستخدمين

- إضافة واجهة لعرض قائمة المستخدمين
- إمكانية إضافة وتعديل المستخدمين
- نظام صلاحيات متقدم
```

## 🔍 مراجعة الكود

عند مراجعة Pull Requests:

- تحقق من جودة الكود
- اختبر الوظائف الجديدة
- تأكد من عدم كسر الوظائف الموجودة
- قدم ملاحظات بناءة

## 📞 التواصل

للأسئلة أو المساعدة:
- أنشئ Issue على GitHub
- تواصل مع فريق التطوير

## 📄 الترخيص

بمساهمتك في هذا المشروع، فإنك توافق على أن تكون مساهماتك مرخصة تحت رخصة MIT.

---

شكراً لمساهمتكم في تطوير النظام! 🙏
