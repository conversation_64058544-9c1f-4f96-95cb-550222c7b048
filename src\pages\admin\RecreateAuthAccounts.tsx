import { useState } from 'react';
import { recreateAllAuthAccounts } from '../../lib/initializeAdmin';

const RecreateAuthAccounts = () => {
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState<string | null>(null);
  const [notification, setNotification] = useState<{message: string, type: 'success' | 'error'} | null>(null);

  const showNotification = (message: string, type: 'success' | 'error') => {
    setNotification({ message, type });
    setTimeout(() => setNotification(null), 5000);
  };

  const handleRecreateAccounts = async () => {
    setLoading(true);
    setResult(null);
    
    try {
      console.log('🔄 بدء عملية إعادة إنشاء الحسابات...');
      
      const success = await recreateAllAuthAccounts();
      
      if (success) {
        setResult('✅ تم إعادة إنشاء حسابات المصادقة بنجاح! جميع المستخدمين يمكنهم الآن تسجيل الدخول بكلمة المرور: hemam2025');
        showNotification('تم إعادة إنشاء الحسابات بنجاح', 'success');
      } else {
        setResult('❌ فشل في إعادة إنشاء بعض الحسابات. تحقق من وحدة التحكم للمزيد من التفاصيل.');
        showNotification('فشل في إعادة إنشاء الحسابات', 'error');
      }
    } catch (error) {
      console.error('خطأ في إعادة إنشاء الحسابات:', error);
      setResult('❌ حدث خطأ غير متوقع أثناء إعادة إنشاء الحسابات.');
      showNotification('حدث خطأ غير متوقع', 'error');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-100 p-4">
      {/* إشعار */}
      {notification && (
        <div className={`fixed top-4 right-4 p-4 rounded-lg shadow-lg z-50 ${
          notification.type === 'success' ? 'bg-green-500 text-white' : 'bg-red-500 text-white'
        }`}>
          {notification.message}
        </div>
      )}
      
      <div className="max-w-4xl mx-auto space-y-6">
        <div className="bg-white rounded-lg shadow-sm border border-neutral-200 p-6">
          <div className="flex items-center justify-between mb-6">
            <h1 className="text-2xl font-bold text-neutral-900">
              إعادة إنشاء حسابات المصادقة
            </h1>
            <a 
              href="/test-login" 
              className="text-blue-600 hover:text-blue-800 text-sm underline"
            >
              اختبار تسجيل الدخول
            </a>
          </div>

          <div className="space-y-6">
            <div className="bg-red-50 border border-red-200 rounded-lg p-4">
              <h3 className="font-semibold text-red-900 mb-2">🚨 مشكلة في الصلاحيات</h3>
              <p className="text-red-800 text-sm mb-3">
                تم اكتشاف أن التطبيق لا يملك صلاحيات Admin API المطلوبة.
                يجب إصلاح المشكلة يدوياً من لوحة تحكم Supabase.
              </p>
              <a
                href="/fix-auth"
                className="inline-block bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded text-sm"
              >
                🔧 اذهب إلى دليل الإصلاح
              </a>
            </div>

            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <h3 className="font-semibold text-blue-900 mb-2">📋 ما ستفعله هذه العملية:</h3>
              <ul className="text-blue-800 text-sm space-y-1">
                <li>• حذف جميع حسابات المصادقة الموجودة للمستخدمين</li>
                <li>• إنشاء حسابات مصادقة جديدة بكلمة المرور الموحدة: <strong>hemam2025</strong></li>
                <li>• ربط الحسابات الجديدة بجدول المستخدمين</li>
                <li>• تفعيل البريد الإلكتروني تلقائياً</li>
              </ul>
            </div>

            <div className="bg-green-50 border border-green-200 rounded-lg p-4">
              <h3 className="font-semibold text-green-900 mb-2">👥 المستخدمون المشمولون:</h3>
              <div className="grid md:grid-cols-2 gap-2 text-green-800 text-sm">
                <div>• معتصم العرفج - <EMAIL></div>
                <div>• سعد الدريهم - <EMAIL></div>
                <div>• أحمد الدريهم - <EMAIL></div>
                <div>• عبدالعزيز المطرد - <EMAIL></div>
                <div>• عبدالله المحسن - <EMAIL></div>
                <div>• سعد القحيز - <EMAIL></div>
                <div>• ناصر اليحيى - <EMAIL></div>
              </div>
            </div>

            <div className="flex justify-center">
              <button
                onClick={handleRecreateAccounts}
                disabled={loading}
                className={`px-8 py-3 rounded-lg font-medium transition-colors ${
                  loading 
                    ? 'bg-gray-400 text-white cursor-not-allowed' 
                    : 'bg-red-600 hover:bg-red-700 text-white'
                }`}
              >
                {loading ? '🔄 جاري إعادة الإنشاء...' : '🔄 إعادة إنشاء جميع الحسابات'}
              </button>
            </div>

            {result && (
              <div className={`p-4 rounded-lg ${
                result.includes('✅') ? 'bg-green-50 border border-green-200' : 'bg-red-50 border border-red-200'
              }`}>
                <p className={`${
                  result.includes('✅') ? 'text-green-800' : 'text-red-800'
                }`}>
                  {result}
                </p>
              </div>
            )}

            <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
              <h3 className="font-semibold text-gray-900 mb-2">🔍 للتحقق من النتائج:</h3>
              <ol className="text-gray-700 text-sm space-y-1 list-decimal list-inside">
                <li>انتظر حتى تكتمل العملية</li>
                <li>اذهب إلى صفحة اختبار تسجيل الدخول</li>
                <li>جرب تسجيل الدخول بأي من المستخدمين</li>
                <li>استخدم كلمة المرور: <strong>hemam2025</strong></li>
              </ol>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default RecreateAuthAccounts;
