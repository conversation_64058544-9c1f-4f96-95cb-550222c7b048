import { useState } from 'react';
import { createAuthAccountForUser } from '../../lib/initializeAdmin';
import { UserService } from '../../lib/userService';
import { useNotifications } from '../../components/ui/NotificationSystem';

const CreateAuthAccounts = () => {
  const [loading, setLoading] = useState(false);
  const [users, setUsers] = useState<any[]>([]);
  const [loadingUsers, setLoadingUsers] = useState(false);
  const { showNotification } = useNotifications();

  // جلب المستخدمين الذين ليس لديهم حسابات مصادقة
  const loadUsersWithoutAuth = async () => {
    setLoadingUsers(true);
    try {
      const allUsers = await UserService.getAllUsers();
      const usersWithoutAuth = allUsers.filter(user => !user.auth_id || user.auth_id === null);
      setUsers(usersWithoutAuth);
      showNotification('تم جلب المستخدمين بنجاح', 'success');
    } catch (error) {
      console.error('Error loading users:', error);
      showNotification('خطأ في جلب المستخدمين', 'error');
    } finally {
      setLoadingUsers(false);
    }
  };

  // إنشاء حساب مصادقة لمستخدم محدد
  const createAuthForUser = async (email: string) => {
    setLoading(true);
    try {
      const success = await createAuthAccountForUser(email);
      if (success) {
        showNotification(`تم إنشاء حساب المصادقة للمستخدم ${email} بنجاح`, 'success');
        // إعادة تحميل القائمة
        await loadUsersWithoutAuth();
      } else {
        showNotification(`فشل في إنشاء حساب المصادقة للمستخدم ${email}`, 'error');
      }
    } catch (error) {
      console.error('Error creating auth account:', error);
      showNotification('حدث خطأ غير متوقع', 'error');
    } finally {
      setLoading(false);
    }
  };

  // إنشاء حسابات مصادقة لجميع المستخدمين
  const createAuthForAllUsers = async () => {
    setLoading(true);
    try {
      let successCount = 0;
      let failCount = 0;

      for (const user of users) {
        try {
          const success = await createAuthAccountForUser(user.email);
          if (success) {
            successCount++;
          } else {
            failCount++;
          }
        } catch (error) {
          failCount++;
          console.error(`Error creating auth for ${user.email}:`, error);
        }
      }

      showNotification(
        `تم إنشاء ${successCount} حساب بنجاح، فشل في ${failCount} حساب`,
        successCount > 0 ? 'success' : 'error'
      );

      // إعادة تحميل القائمة
      await loadUsersWithoutAuth();
    } catch (error) {
      console.error('Error creating auth accounts:', error);
      showNotification('حدث خطأ غير متوقع', 'error');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      <div className="bg-white rounded-lg shadow-sm border border-neutral-200 p-6">
        <h1 className="text-2xl font-bold text-neutral-900 mb-6">
          إنشاء حسابات المصادقة
        </h1>

        <div className="space-y-4">
          <div className="flex gap-4">
            <button
              onClick={loadUsersWithoutAuth}
              disabled={loadingUsers}
              className="btn-primary"
            >
              {loadingUsers ? 'جاري التحميل...' : 'جلب المستخدمين بدون حسابات مصادقة'}
            </button>

            {users.length > 0 && (
              <button
                onClick={createAuthForAllUsers}
                disabled={loading}
                className="btn-secondary"
              >
                {loading ? 'جاري الإنشاء...' : 'إنشاء حسابات لجميع المستخدمين'}
              </button>
            )}
          </div>

          {users.length > 0 && (
            <div className="bg-neutral-50 rounded-lg p-4">
              <h3 className="text-lg font-semibold text-neutral-900 mb-4">
                المستخدمون بدون حسابات مصادقة ({users.length})
              </h3>
              
              <div className="space-y-3">
                {users.map((user) => (
                  <div
                    key={user.id}
                    className="flex items-center justify-between bg-white p-4 rounded-lg border border-neutral-200"
                  >
                    <div>
                      <div className="font-medium text-neutral-900">{user.name}</div>
                      <div className="text-sm text-neutral-600">{user.email}</div>
                      <div className="text-sm text-neutral-500">
                        {user.role} - {user.department}
                      </div>
                    </div>
                    
                    <button
                      onClick={() => createAuthForUser(user.email)}
                      disabled={loading}
                      className="btn-primary text-sm"
                    >
                      {loading ? 'جاري الإنشاء...' : 'إنشاء حساب'}
                    </button>
                  </div>
                ))}
              </div>
            </div>
          )}

          {users.length === 0 && !loadingUsers && (
            <div className="text-center py-8 text-neutral-500">
              لا توجد مستخدمون بدون حسابات مصادقة
            </div>
          )}
        </div>

        <div className="mt-8 p-4 bg-blue-50 rounded-lg">
          <h4 className="font-semibold text-blue-900 mb-2">ملاحظات مهمة:</h4>
          <ul className="text-sm text-blue-800 space-y-1">
            <li>• كلمة المرور الافتراضية لجميع الحسابات: Strategic@123</li>
            <li>• يمكن للمستخدمين تغيير كلمة المرور بعد تسجيل الدخول</li>
            <li>• سيتم ربط حساب المصادقة بالمستخدم في جدول users</li>
            <li>• تأكد من إبلاغ المستخدمين ببيانات الدخول الخاصة بهم</li>
          </ul>
        </div>
      </div>
    </div>
  );
};

export default CreateAuthAccounts;
